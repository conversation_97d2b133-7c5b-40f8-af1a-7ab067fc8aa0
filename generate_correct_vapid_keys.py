#!/usr/bin/env python3
"""
Correct VAPID key generator using the official py-vapid library.
This generates keys in the exact format expected by pywebpush and web browsers.
"""

import secrets
import base64

def generate_vapid_keys_with_py_vapid():
    """Generate VAPID keys using the official py-vapid library."""
    try:
        from vapid import Vapid
        
        print("🔄 Generating VAPID keys using py-vapid library...")
        
        # Generate a new pair of VAPID keys
        vapid_instance = Vapid()
        vapid_keys = vapid_instance.generate_keys()
        
        # Extract the public and private keys
        public_key = vapid_keys["public_key"]
        private_key = vapid_keys["private_key"]
        
        return public_key, private_key
        
    except ImportError:
        print("❌ py-vapid library not found!")
        print("Please install it with: pip install py-vapid")
        return None, None
    except Exception as e:
        print(f"❌ Error generating keys with py-vapid: {e}")
        return None, None

def generate_secret_key():
    """Generate a secure secret key for the application."""
    return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

def validate_vapid_keys(public_key, private_key):
    """Validate the generated VAPID keys."""
    validation_results = []
    
    # Check key lengths
    if len(public_key) == 87:
        validation_results.append("✅ Public key length is correct (87 characters)")
    else:
        validation_results.append(f"⚠️  Public key length is {len(public_key)}, expected 87")
    
    if len(private_key) == 43:
        validation_results.append("✅ Private key length is correct (43 characters)")
    else:
        validation_results.append(f"⚠️  Private key length is {len(private_key)}, expected 43")
    
    # Check if keys start with expected prefixes
    if public_key.startswith('B'):
        validation_results.append("✅ Public key starts with 'B' (uncompressed format)")
    else:
        validation_results.append(f"⚠️  Public key starts with '{public_key[0]}', expected 'B'")
    
    # Test base64url decoding
    try:
        decoded_public = base64.urlsafe_b64decode(public_key + '==')
        if len(decoded_public) == 65 and decoded_public[0] == 0x04:
            validation_results.append("✅ Public key decodes to correct format (65 bytes, starts with 0x04)")
        else:
            validation_results.append(f"⚠️  Public key decoding issue: {len(decoded_public)} bytes, starts with 0x{decoded_public[0]:02x}")
    except Exception as e:
        validation_results.append(f"❌ Public key decoding error: {e}")
    
    try:
        decoded_private = base64.urlsafe_b64decode(private_key + '==')
        if len(decoded_private) == 32:
            validation_results.append("✅ Private key decodes to correct format (32 bytes)")
        else:
            validation_results.append(f"⚠️  Private key decoding issue: {len(decoded_private)} bytes")
    except Exception as e:
        validation_results.append(f"❌ Private key decoding error: {e}")
    
    return validation_results

def main():
    print("=" * 80)
    print("🔑 OFFICIAL VAPID Key Generator using py-vapid")
    print("=" * 80)
    print("This uses the official py-vapid library to generate keys")
    print("that are guaranteed to work with pywebpush and web browsers.")
    print("=" * 80)
    
    # Generate VAPID keys
    public_key, private_key = generate_vapid_keys_with_py_vapid()
    
    if not public_key or not private_key:
        print("\n❌ Failed to generate VAPID keys!")
        return None
    
    # Generate secret key
    secret_key = generate_secret_key()
    
    print(f"\n✅ Keys generated successfully!")
    
    # Validate the keys
    print(f"\n🔍 VALIDATION RESULTS:")
    print("-" * 50)
    validation_results = validate_vapid_keys(public_key, private_key)
    for result in validation_results:
        print(result)
    
    print(f"\n📊 KEY INFORMATION:")
    print("-" * 50)
    print(f"• Public key length: {len(public_key)} characters")
    print(f"• Private key length: {len(private_key)} characters")
    print(f"• Secret key length: {len(secret_key)} characters")
    print(f"• Generated using: py-vapid library (official)")
    
    print("\n" + "=" * 80)
    print("📋 COPY THESE VALUES TO YOUR .env FILE:")
    print("=" * 80)
    
    print(f"\n# VAPID Keys for Web Push (Generated with official py-vapid)")
    print(f"VAPID_PUBLIC_KEY={public_key}")
    print(f"VAPID_PRIVATE_KEY={private_key}")
    print(f"VAPID_SUBJECT=https://gamyday-notification.onrender.com")
    
    print(f"\n# Application Secret Key")
    print(f"SECRET_KEY={secret_key}")
    
    print("\n" + "=" * 80)
    print("📋 FOR YOUR FRONTEND .env FILE:")
    print("=" * 80)
    print(f"NEXT_PUBLIC_VAPID_PUBLIC_KEY={public_key}")
    
    print("\n" + "=" * 80)
    print("🚀 DEPLOYMENT INSTRUCTIONS:")
    print("=" * 80)
    print("1. Update your Render.com environment variables with the new keys")
    print("2. Redeploy your service")
    print("3. Test the subscription process")
    print("4. All existing subscriptions will become invalid (users need to re-subscribe)")
    
    print("\n" + "=" * 80)
    print("⚠️  IMPORTANT NOTES:")
    print("=" * 80)
    print("• These keys are generated using the OFFICIAL py-vapid library")
    print("• They are guaranteed to work with pywebpush and web browsers")
    print("• The format is exactly what the Web Push Protocol expects")
    print("• Keep your private key secure and never share it publicly")
    
    return {
        'public_key': public_key,
        'private_key': private_key,
        'secret_key': secret_key,
        'validation_results': validation_results
    }

if __name__ == "__main__":
    result = main()
    if result:
        print(f"\n🎉 VAPID key generation completed successfully!")
        print(f"🔧 Ready to update your server configuration!")
    else:
        print(f"\n💥 VAPID key generation failed!")
        exit(1)
