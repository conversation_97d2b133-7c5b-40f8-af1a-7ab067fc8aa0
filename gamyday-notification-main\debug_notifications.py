#!/usr/bin/env python3
"""Debug script to test notification sending."""

import asyncio
import sys
import json
import logging
from app.database import db
from app.push_service import push_service
from app.models import NotificationPayload

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_notifications():
    """Debug notification sending process."""
    try:
        # Connect to database
        await db.connect()
        logger.info("✅ Connected to database")
        
        # Get active subscriptions
        subscriptions = await db.get_active_subscriptions()
        logger.info(f"📊 Found {len(subscriptions)} active subscriptions")
        
        if not subscriptions:
            logger.error("❌ No active subscriptions found!")
            return
        
        # Show first subscription structure
        logger.info("🔍 First subscription structure:")
        print(json.dumps(subscriptions[0], indent=2, default=str))
        
        # Create a test notification payload
        payload = NotificationPayload(
            title="Debug Test Notification",
            body="This is a test notification from debug script",
            url="/",
            icon="/favicon.png",
            data={"url": "/"}
        )
        
        logger.info("📤 Testing notification to first subscription...")
        
        # Test sending to first subscription
        first_subscription = subscriptions[0]
        success = await push_service.send_notification_to_subscription(first_subscription, payload)
        
        if success:
            logger.info("✅ Successfully sent notification to first subscription")
        else:
            logger.error("❌ Failed to send notification to first subscription")
        
        # Test sending to all subscriptions
        logger.info("📤 Testing notification to all subscriptions...")
        result = await push_service.send_notification_to_all(payload)
        
        logger.info(f"📊 Batch result: {result}")
        
    except Exception as e:
        logger.error(f"❌ Error during debug: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await db.disconnect()
        logger.info("🔌 Disconnected from database")

if __name__ == "__main__":
    asyncio.run(debug_notifications())
