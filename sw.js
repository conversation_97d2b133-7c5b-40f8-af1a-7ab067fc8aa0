// Service Worker for Web Push Notifications
// This file handles incoming push notifications and displays them to the user

const CACHE_NAME = 'push-notifications-v1';
const urlsToCache = [
    '/',
    '/push-notification-test.html',
    '/sw.js'
];

// Install event - cache resources
self.addEventListener('install', event => {
    console.log('[SW] Install event');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('[SW] Opened cache');
                return cache.addAll(urlsToCache);
            })
            .catch(error => {
                console.error('[SW] Cache installation failed:', error);
            })
    );
    
    // Force the waiting service worker to become the active service worker
    self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('[SW] Activate event');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('[SW] Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
    
    // Ensure the service worker takes control of all clients immediately
    self.clients.claim();
});

// Push event - handle incoming push notifications
self.addEventListener('push', event => {
    console.log('[SW] Push event received');
    
    let notificationData = {
        title: 'Default Title',
        body: 'Default message',
        icon: '/favicon.png',
        badge: '/favicon.png',
        data: { url: '/' },
        requireInteraction: false,
        actions: []
    };
    
    // Parse the push data if available
    if (event.data) {
        try {
            const pushData = event.data.json();
            console.log('[SW] Push data received:', pushData);
            
            // Merge with default data
            notificationData = {
                ...notificationData,
                ...pushData
            };
            
        } catch (error) {
            console.error('[SW] Error parsing push data:', error);
            // Use text data as body if JSON parsing fails
            notificationData.body = event.data.text() || notificationData.body;
        }
    }
    
    // Notification options
    const options = {
        body: notificationData.body,
        icon: notificationData.icon,
        badge: notificationData.badge,
        image: notificationData.image,
        data: notificationData.data,
        requireInteraction: notificationData.requireInteraction,
        actions: notificationData.actions,
        tag: notificationData.tag,
        renotify: true,
        vibrate: [200, 100, 200], // Vibration pattern for mobile devices
        timestamp: Date.now()
    };
    
    // Show the notification
    event.waitUntil(
        self.registration.showNotification(notificationData.title, options)
            .then(() => {
                console.log('[SW] Notification displayed successfully');
            })
            .catch(error => {
                console.error('[SW] Error displaying notification:', error);
            })
    );
});

// Notification click event - handle user interaction with notifications
self.addEventListener('notificationclick', event => {
    console.log('[SW] Notification click event');
    
    const notification = event.notification;
    const action = event.action;
    const data = notification.data || {};
    
    // Close the notification
    notification.close();
    
    // Handle action buttons
    if (action) {
        console.log('[SW] Action clicked:', action);
        
        // Handle different actions
        switch (action) {
            case 'open':
                // Open the URL in the data
                if (data.url) {
                    event.waitUntil(
                        clients.openWindow(data.url)
                    );
                }
                break;
            case 'dismiss':
                // Just close the notification (already done above)
                console.log('[SW] Notification dismissed');
                break;
            default:
                console.log('[SW] Unknown action:', action);
        }
    } else {
        // Default click behavior - open the URL
        const urlToOpen = data.url || '/';
        
        event.waitUntil(
            clients.matchAll({
                type: 'window',
                includeUncontrolled: true
            }).then(clientList => {
                // Check if there's already a window/tab open with the target URL
                for (let i = 0; i < clientList.length; i++) {
                    const client = clientList[i];
                    if (client.url === urlToOpen && 'focus' in client) {
                        console.log('[SW] Focusing existing window');
                        return client.focus();
                    }
                }
                
                // If no existing window, open a new one
                if (clients.openWindow) {
                    console.log('[SW] Opening new window:', urlToOpen);
                    return clients.openWindow(urlToOpen);
                }
            }).catch(error => {
                console.error('[SW] Error handling notification click:', error);
            })
        );
    }
});

// Notification close event - handle when user dismisses notification
self.addEventListener('notificationclose', event => {
    console.log('[SW] Notification close event');
    
    const notification = event.notification;
    const data = notification.data || {};
    
    // You can track notification dismissals here
    console.log('[SW] Notification dismissed:', notification.title);
    
    // Optional: Send analytics data about notification dismissal
    // This could be useful for tracking engagement
});

// Background sync event - handle background synchronization
self.addEventListener('sync', event => {
    console.log('[SW] Background sync event:', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(
            // Perform background sync operations here
            console.log('[SW] Performing background sync')
        );
    }
});

// Message event - handle messages from the main thread
self.addEventListener('message', event => {
    console.log('[SW] Message received:', event.data);
    
    if (event.data && event.data.type) {
        switch (event.data.type) {
            case 'SKIP_WAITING':
                self.skipWaiting();
                break;
            case 'GET_VERSION':
                event.ports[0].postMessage({ version: CACHE_NAME });
                break;
            default:
                console.log('[SW] Unknown message type:', event.data.type);
        }
    }
});

// Fetch event - handle network requests (optional caching strategy)
self.addEventListener('fetch', event => {
    // Only handle GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    // Skip cross-origin requests
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return cached version if available
                if (response) {
                    return response;
                }
                
                // Otherwise fetch from network
                return fetch(event.request)
                    .then(response => {
                        // Don't cache if not a valid response
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Clone the response
                        const responseToCache = response.clone();
                        
                        // Add to cache
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                cache.put(event.request, responseToCache);
                            });
                        
                        return response;
                    });
            })
            .catch(error => {
                console.error('[SW] Fetch error:', error);
                // Return a fallback response if needed
            })
    );
});

// Error event - handle service worker errors
self.addEventListener('error', event => {
    console.error('[SW] Service worker error:', event.error);
});

// Unhandled rejection event - handle promise rejections
self.addEventListener('unhandledrejection', event => {
    console.error('[SW] Unhandled promise rejection:', event.reason);
    event.preventDefault();
});

console.log('[SW] Service worker script loaded');
