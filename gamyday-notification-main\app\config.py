"""Configuration settings for the notification server."""

import os
from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # MongoDB Configuration
    mongodb_url: str = "mongodb://localhost:27017"
    database_name: str = "gamyday_notifications"
    
    # VAPID Configuration
    vapid_public_key: str = ""
    vapid_private_key: str = ""
    vapid_subject: str = "mailto:<EMAIL>"
    
    # Server Configuration
    port: int = 8000
    host: str = "0.0.0.0"
    debug: bool = True
    
    # CORS Configuration
    allowed_origins: str = "http://localhost:3000"
    
    # Admin Panel Configuration
    admin_username: str = "admin"
    admin_password: str = "Admin123"
    
    # Security
    secret_key: str = "1xx9FRDxMMxfuRhntcgSRRwlkfaDW152-G04bV8Zmhw"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
    
    @property
    def allowed_origins_list(self) -> List[str]:
        """Convert comma-separated origins to list."""
        return [origin.strip() for origin in self.allowed_origins.split(",")]


# Global settings instance
settings = Settings()
