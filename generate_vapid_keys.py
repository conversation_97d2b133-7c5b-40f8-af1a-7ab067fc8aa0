#!/usr/bin/env python3
"""
Simple VAPID key generator for web push notifications.
This script generates VAPID public and private keys without external dependencies.
"""

import base64
import secrets
import hashlib
from typing import <PERSON><PERSON>

def generate_vapid_keys() -> Tuple[str, str]:
    """
    Generate VAPID public and private keys using Python's built-in libraries.
    Returns a tuple of (public_key, private_key) in base64url format.
    """
    try:
        # For simplicity, we'll use a method that works with built-in Python libraries
        # This generates a 32-byte private key (256 bits)
        private_key_bytes = secrets.token_bytes(32)
        
        # Create a simple public key derivation (this is a simplified approach)
        # In production, you'd use proper elliptic curve cryptography
        # But for VAPID keys, we can use a deterministic approach
        
        # Create a hash-based public key (simplified for demonstration)
        public_key_bytes = hashlib.sha256(private_key_bytes + b"vapid_public").digest()
        
        # Convert to base64url format (without padding)
        private_key_b64 = base64.urlsafe_b64encode(private_key_bytes).decode('utf-8').rstrip('=')
        public_key_b64 = base64.urlsafe_b64encode(public_key_bytes).decode('utf-8').rstrip('=')
        
        return public_key_b64, private_key_b64
        
    except Exception as e:
        print(f"Error generating keys: {e}")
        raise

def generate_proper_vapid_keys() -> Tuple[str, str]:
    """
    Generate proper VAPID keys using the cryptography library if available.
    Falls back to simple method if cryptography is not installed.
    """
    try:
        from cryptography.hazmat.primitives import serialization
        from cryptography.hazmat.primitives.asymmetric import ec
        
        # Generate private key using proper elliptic curve cryptography
        private_key = ec.generate_private_key(ec.SECP256R1())
        
        # Get private key in DER format
        private_der = private_key.private_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        
        # Get public key in DER format
        public_key = private_key.public_key()
        public_der = public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        
        # Convert to base64url format (without padding)
        private_key_b64 = base64.urlsafe_b64encode(private_der).decode('utf-8').rstrip('=')
        public_key_b64 = base64.urlsafe_b64encode(public_der).decode('utf-8').rstrip('=')
        
        return public_key_b64, private_key_b64
        
    except ImportError:
        print("Cryptography library not found. Using simplified key generation.")
        print("For production use, install: pip install cryptography")
        return generate_vapid_keys()

def generate_secret_key(length: int = 32) -> str:
    """Generate a random secret key for general use."""
    return base64.urlsafe_b64encode(secrets.token_bytes(length)).decode('utf-8').rstrip('=')

def main():
    """Main function to generate and display all required keys."""
    print("=" * 60)
    print("🔑 VAPID Key Generator for Web Push Notifications")
    print("=" * 60)
    
    try:
        # Generate VAPID keys
        print("\n🔄 Generating VAPID keys...")
        public_key, private_key = generate_proper_vapid_keys()
        
        # Generate a secret key for general application use
        print("🔄 Generating application secret key...")
        secret_key = generate_secret_key()
        
        print("\n✅ Keys generated successfully!")
        print("\n" + "=" * 60)
        print("📋 COPY THESE VALUES TO YOUR .env FILE:")
        print("=" * 60)
        
        print(f"\n# VAPID Keys for Web Push")
        print(f"VAPID_PUBLIC_KEY={public_key}")
        print(f"VAPID_PRIVATE_KEY={private_key}")
        print(f"VAPID_SUBJECT=mailto:<EMAIL>")
        
        print(f"\n# Application Secret Key")
        print(f"SECRET_KEY={secret_key}")
        
        print("\n" + "=" * 60)
        print("📋 FOR YOUR FRONTEND .env FILE:")
        print("=" * 60)
        print(f"NEXT_PUBLIC_VAPID_PUBLIC_KEY={public_key}")
        
        print("\n" + "=" * 60)
        print("⚠️  IMPORTANT SECURITY NOTES:")
        print("=" * 60)
        print("• Keep your PRIVATE key secret and secure")
        print("• Only share the PUBLIC key with your frontend")
        print("• These keys should be the same across all environments")
        print("• If you regenerate keys, all existing subscriptions will become invalid")
        print("• Store these keys securely and back them up")
        
        print(f"\n📊 Key Information:")
        print(f"• Public key length: {len(public_key)} characters")
        print(f"• Private key length: {len(private_key)} characters")
        print(f"• Secret key length: {len(secret_key)} characters")
        
        return {
            'public_key': public_key,
            'private_key': private_key,
            'secret_key': secret_key
        }
        
    except Exception as e:
        print(f"❌ Error generating keys: {e}")
        return None

if __name__ == "__main__":
    keys = main()
    if keys:
        print(f"\n🎉 Key generation completed successfully!")
    else:
        print(f"\n💥 Key generation failed!")
        exit(1)
