#!/usr/bin/env python3
"""Generate VAPID keys using py-vapid library."""

from vapid import Vapid
import secrets
import base64

def main():
    print("Generating VAPID keys using py-vapid...")
    
    # Generate VAPID keys
    vapid_instance = Vapid()
    vapid_keys = vapid_instance.generate_keys()
    
    public_key = vapid_keys["public_key"]
    private_key = vapid_keys["private_key"]
    
    # Generate secret key
    secret_key = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    print(f"\nVAPID_PUBLIC_KEY={public_key}")
    print(f"VAPID_PRIVATE_KEY={private_key}")
    print(f"SECRET_KEY={secret_key}")
    
    print(f"\nPublic key length: {len(public_key)}")
    print(f"Private key length: {len(private_key)}")

if __name__ == "__main__":
    main()
