#!/usr/bin/env python3
import asyncio
import sys
sys.path.append('.')
from app.database import db

async def check_subscriptions_after_test():
    await db.connect()
    stats = await db.get_stats()
    print('Stats after test:')
    print(f'  Total subscriptions: {stats["total_subscriptions"]}')
    print(f'  Active subscriptions: {stats["active_subscriptions"]}')
    await db.disconnect()

asyncio.run(check_subscriptions_after_test())
