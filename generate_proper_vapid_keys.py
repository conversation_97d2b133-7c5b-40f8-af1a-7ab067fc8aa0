#!/usr/bin/env python3
"""
Proper VAPID key generator for web push notifications.
This generates keys in the exact format expected by web browsers.
"""

import base64
import secrets

def generate_web_push_vapid_keys():
    """
    Generate VAPID keys in the format expected by web browsers.
    Returns (public_key, private_key) in base64url format.
    """
    try:
        from cryptography.hazmat.primitives import serialization
        from cryptography.hazmat.primitives.asymmetric import ec
        
        # Generate private key using SECP256R1 (P-256) curve
        private_key = ec.generate_private_key(ec.SECP256R1())
        
        # Get the public key
        public_key = private_key.public_key()
        
        # Get private key as raw bytes (32 bytes for P-256)
        private_numbers = private_key.private_numbers()
        private_key_bytes = private_numbers.private_value.to_bytes(32, 'big')
        
        # Get public key in uncompressed format (65 bytes: 0x04 + 32 bytes X + 32 bytes Y)
        public_numbers = public_key.public_numbers()
        x_bytes = public_numbers.x.to_bytes(32, 'big')
        y_bytes = public_numbers.y.to_bytes(32, 'big')
        public_key_bytes = b'\x04' + x_bytes + y_bytes
        
        # Convert to base64url format (without padding)
        private_key_b64 = base64.urlsafe_b64encode(private_key_bytes).decode('utf-8').rstrip('=')
        public_key_b64 = base64.urlsafe_b64encode(public_key_bytes).decode('utf-8').rstrip('=')
        
        return public_key_b64, private_key_b64
        
    except ImportError:
        print("❌ Cryptography library not found!")
        print("Please install it with: pip install cryptography")
        return None, None

def generate_fallback_keys():
    """
    Generate keys using a simpler method if cryptography is not available.
    Note: This is for testing only and may not work with all push services.
    """
    print("⚠️  Using fallback key generation (not recommended for production)")
    
    # Generate 32 random bytes for private key
    private_key_bytes = secrets.token_bytes(32)
    
    # Create a mock public key (65 bytes with 0x04 prefix)
    # This is NOT cryptographically correct but might work for testing
    public_key_bytes = b'\x04' + secrets.token_bytes(64)
    
    # Convert to base64url
    private_key_b64 = base64.urlsafe_b64encode(private_key_bytes).decode('utf-8').rstrip('=')
    public_key_b64 = base64.urlsafe_b64encode(public_key_bytes).decode('utf-8').rstrip('=')
    
    return public_key_b64, private_key_b64

def main():
    print("=" * 70)
    print("🔑 Proper VAPID Key Generator for Web Push Notifications")
    print("=" * 70)
    
    # Try to generate proper keys first
    public_key, private_key = generate_web_push_vapid_keys()
    
    if not public_key:
        print("\n🔄 Falling back to simple key generation...")
        public_key, private_key = generate_fallback_keys()
        
        if not public_key:
            print("❌ Failed to generate keys!")
            return None
    
    # Generate secret key
    secret_key = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    print(f"\n✅ Keys generated successfully!")
    print(f"\n📊 Key Information:")
    print(f"• Public key length: {len(public_key)} characters")
    print(f"• Private key length: {len(private_key)} characters")
    print(f"• Expected public key length: 87 characters (65 bytes)")
    print(f"• Expected private key length: 43 characters (32 bytes)")
    
    print("\n" + "=" * 70)
    print("📋 COPY THESE VALUES TO YOUR .env FILE:")
    print("=" * 70)
    
    print(f"\n# VAPID Keys for Web Push (Proper format for browsers)")
    print(f"VAPID_PUBLIC_KEY={public_key}")
    print(f"VAPID_PRIVATE_KEY={private_key}")
    print(f"VAPID_SUBJECT=https://gamyday-notification.onrender.com")
    
    print(f"\n# Application Secret Key")
    print(f"SECRET_KEY={secret_key}")
    
    print("\n" + "=" * 70)
    print("📋 FOR YOUR FRONTEND .env FILE:")
    print("=" * 70)
    print(f"NEXT_PUBLIC_VAPID_PUBLIC_KEY={public_key}")
    
    print("\n" + "=" * 70)
    print("🔍 VALIDATION:")
    print("=" * 70)
    
    # Validate key lengths
    if len(public_key) == 87:
        print("✅ Public key length is correct (87 characters)")
    else:
        print(f"⚠️  Public key length is {len(public_key)}, expected 87")
    
    if len(private_key) == 43:
        print("✅ Private key length is correct (43 characters)")
    else:
        print(f"⚠️  Private key length is {len(private_key)}, expected 43")
    
    # Test base64url decoding
    try:
        decoded_public = base64.urlsafe_b64decode(public_key + '==')
        if len(decoded_public) == 65 and decoded_public[0] == 0x04:
            print("✅ Public key format is correct (65 bytes, starts with 0x04)")
        else:
            print(f"⚠️  Public key format issue: {len(decoded_public)} bytes, starts with 0x{decoded_public[0]:02x}")
    except Exception as e:
        print(f"❌ Public key decoding error: {e}")
    
    try:
        decoded_private = base64.urlsafe_b64decode(private_key + '==')
        if len(decoded_private) == 32:
            print("✅ Private key format is correct (32 bytes)")
        else:
            print(f"⚠️  Private key format issue: {len(decoded_private)} bytes")
    except Exception as e:
        print(f"❌ Private key decoding error: {e}")
    
    print("\n" + "=" * 70)
    print("⚠️  IMPORTANT NOTES:")
    print("=" * 70)
    print("• These keys are in the correct format for web browsers")
    print("• Public key should be exactly 87 characters (65 bytes when decoded)")
    print("• Private key should be exactly 43 characters (32 bytes when decoded)")
    print("• Update your server with these keys and restart/redeploy")
    print("• All existing subscriptions will become invalid")
    
    return {
        'public_key': public_key,
        'private_key': private_key,
        'secret_key': secret_key
    }

if __name__ == "__main__":
    keys = main()
    if keys:
        print(f"\n🎉 Key generation completed successfully!")
    else:
        print(f"\n💥 Key generation failed!")
        exit(1)
